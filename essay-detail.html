<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作文详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .time { color: #000; }
        .signal { color: #000; }

        /* 顶部导航栏 */
        .header {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .back-btn {
            font-size: 18px;
            color: #333;
            cursor: pointer;
            margin-right: 15px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .content {
            padding: 20px;
            height: calc(100% - 140px);
            overflow-y: auto;
        }

        /* 标签区域 */
        .tags-section {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        /* 评分区域 */
        .score-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .essay-title {
            font-size: 16px;
            color: #333;
        }

        .total-score {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b6b;
        }

        .score-unit {
            font-size: 14px;
            color: #999;
        }

        /* 评价项目 */
        .evaluation-section {
            margin-bottom: 25px;
        }

        .evaluation-item {
            padding: 12px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .evaluation-item:last-child {
            border-bottom: none;
        }

        .evaluation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .evaluation-label {
            font-size: 14px;
            color: #333;
        }

        .evaluation-text {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .stars {
            display: flex;
            gap: 2px;
        }

        .star {
            color: #ffd700;
            font-size: 16px;
        }

        .star.empty {
            color: #ddd;
        }

        /* 总体评价 */
        .overall-review {
            margin-bottom: 30px;
        }

        .review-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .review-content {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
        }



        /* 导航标签栏 */
        .nav-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 20px;
        }

        .nav-tab {
            flex: 1;
            text-align: center;
            padding: 15px 0;
            font-size: 16px;
            color: #666;
            cursor: pointer;
            position: relative;
            border-bottom: 2px solid transparent;
        }

        .nav-tab.active {
            color: #1976d2;
            border-bottom-color: #1976d2;
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 作文报告样式 */
        .report-container {
            margin: 20px 0;
            padding: 0 10px;
        }

        .report-chart {
            width: 100%;
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 8px;
            position: relative;
            background: white;
            margin: 0 auto;
        }

        .chart-line {
            position: absolute;
            background: #ccc;
            height: 2px;
        }

        .line-1 {
            width: 100%;
            top: 50%;
            left: 0;
            transform: translateY(-50%) rotate(45deg);
            transform-origin: center;
        }

        .line-2 {
            width: 100%;
            top: 50%;
            left: 0;
            transform: translateY(-50%) rotate(-45deg);
            transform-origin: center;
        }

        /* 润色作文样式 */
        .polished-essay {
            padding: 20px 0;
        }

        .essay-subtitle {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .essay-content {
            font-size: 16px;
            line-height: 1.8;
            color: #333;
            text-align: justify;
            margin-bottom: 40px;
            padding: 0 5px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            padding: 20px;
            margin-top: 30px;
        }

        .action-btn {
            flex: 1;
            background: #5a67d8;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .action-btn:hover {
            background: #4c51bf;
        }

        /* 作文要求样式 */
        .requirements-container {
            padding: 20px 0;
        }

        .requirement-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .requirement-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 14px;
        }

        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .requirement-item:last-child {
            border-bottom: none;
        }

        .requirement-label {
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }

        .requirement-value {
            font-size: 16px;
            color: #666;
        }

        .requirement-description {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            line-height: 1.6;
            color: #333;
        }

        .upload-section {
            margin-top: 30px;
        }

        .upload-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .help-icon {
            width: 20px;
            height: 20px;
            background: #ccc;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
        }

        .upload-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .upload-box {
            aspect-ratio: 3/2;
            border: 2px solid #ddd;
            border-radius: 8px;
            position: relative;
            background: white;
            cursor: pointer;
        }

        .upload-box .chart-line {
            position: absolute;
            background: #ccc;
            height: 2px;
        }

        .upload-box .line-1 {
            width: 100%;
            top: 50%;
            left: 0;
            transform: translateY(-50%) rotate(45deg);
            transform-origin: center;
        }

        .upload-box .line-2 {
            width: 100%;
            top: 50%;
            left: 0;
            transform: translateY(-50%) rotate(-45deg);
            transform-origin: center;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="header">
            <div class="back-btn" onclick="goBack()">←</div>
            <div class="header-title">详情</div>
        </div>

        <div class="content">
            <!-- 导航标签栏 -->
            <div class="nav-tabs">
                <div class="nav-tab active" onclick="switchTab('essay-review')">作文点评</div>
                <div class="nav-tab" onclick="switchTab('essay-report')">作文报告</div>
                <div class="nav-tab" onclick="switchTab('model-essay')">润色作文</div>
                <div class="nav-tab" onclick="switchTab('essay-requirements')">作文要求</div>
            </div>

            <!-- 作文点评内容 -->
            <div id="essay-review-content" class="tab-content">
                <!-- 评分区域 -->
                <div class="score-section">
                    <div class="essay-title">第一单元 单元作文 全命题</div>
                    <div>
                        <span class="total-score">28</span>
                        <span class="score-unit">分</span>
                    </div>
                </div>

                <!-- 副标题 -->
                <div style="font-size: 16px; color: #333; margin-bottom: 20px;">我的植物朋友</div>

                <!-- 点评标题 -->
                <div style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 15px;">点评</div>

            <!-- 评价项目 -->
            <div class="evaluation-section">
                <div class="evaluation-item">
                    <div class="evaluation-header">
                        <div class="evaluation-label">立意新颖</div>
                        <div class="stars">
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star empty">★</span>
                        </div>
                    </div>
                    <div class="evaluation-text">文章以我的植物朋友为主题，立意清新自然，内容贴近生活，体现了对大自然的热爱和对植物的细致观察。</div>
                </div>

                <div class="evaluation-item">
                    <div class="evaluation-header">
                        <div class="evaluation-label">内容</div>
                        <div class="stars">
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star empty">★</span>
                        </div>
                    </div>
                    <div class="evaluation-text">内容具体详实，描述了植物的外形、习性、作用等多个方面，展现了作者对植物朋友的深入了解和真挚情感。</div>
                </div>

                <div class="evaluation-item">
                    <div class="evaluation-header">
                        <div class="evaluation-label">结构</div>
                        <div class="stars">
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star empty">★</span>
                        </div>
                    </div>
                    <div class="evaluation-text">文章结构较为清晰，但层次感还可以进一步加强，建议在段落安排上更加有序。</div>
                </div>

                <div class="evaluation-item">
                    <div class="evaluation-header">
                        <div class="evaluation-label">语言</div>
                        <div class="stars">
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star empty">★</span>
                        </div>
                    </div>
                    <div class="evaluation-text">语言朴实</div>
                </div>
            </div>

                <!-- 总体评价 -->
                <div class="overall-review">
                    <div class="review-title">总体评价</div>
                    <div class="review-content">
                        本文以植物朋友为主题进行写作，立意清新，内容丰富，文章结构较为清晰，语言朴实自然。作者通过细致的观察和真挚的情感表达，成功地展现了对植物朋友的喜爱之情。文章描述生动具体，能够抓住植物的特点进行描写，体现了作者良好的观察能力。建议在今后的写作中注意段落层次的安排，使文章结构更加清晰明了。同时可以进一步丰富语言表达，运用更多的修辞手法，让文章更加生动有趣。总的来说，这是一篇较好的习作，展现了作者对自然的热爱和细致的观察力，值得肯定。希望作者继续努力，在写作的道路上不断进步。
                    </div>
                </div>
            </div>

            <!-- 作文报告内容 -->
            <div id="essay-report-content" class="tab-content">
                <div class="report-container">
                    <div class="report-chart">
                        <div class="chart-line line-1"></div>
                        <div class="chart-line line-2"></div>
                    </div>
                </div>
            </div>

            <!-- 润色作文内容 -->
            <div id="model-essay-content" class="tab-content">
                <div class="polished-essay">
                    <div class="essay-subtitle">我的植物朋友</div>
                    <div class="essay-content">
                        我家阳台有很多植物朋友呢——仙人掌，它不怕炎热也不怕严寒，也过着自家的好日子呢，如果真的没有色彩斑斓的大花。
                        <br><br>
                        仙人掌外表一看——就像小人心，它就像温暖的朋友，明明长得像个小刺猬，最后变得像了个温暖的小朋友，它的身体是绿色的，满身长着小刺呢，看起来就像穿着绿色的小花衣。
                        <br><br>
                        春天来了，我发现它变了，它变得更加翠绿了，叶子变得更了不起，叶子变得像了不起的小朋友，我发现它变得像了不起的小朋友，我发现它变得像了不起的小朋友，我发现它变得像了不起的小朋友。
                        <br><br>
                        我发现，我的"小朋友"是最棒的了，它真的很厉害呢！上次的它已经变得更厉害了，明明上去就像是温暖的小朋友，它明明上去就像是温暖的小朋友，它明明上去就像是温暖的小朋友。
                    </div>

                    <!-- 操作按钮 -->
                    <div class="action-buttons">
                        <button class="action-btn" onclick="copyEssay()">复制全文</button>
                        <button class="action-btn" onclick="downloadEssay()">下载</button>
                    </div>
                </div>
            </div>

            <!-- 作文要求内容 -->
            <div id="essay-requirements-content" class="tab-content">
                <div class="requirements-container">
                    <!-- 标签区域 -->
                    <div class="requirement-tags">
                        <span class="requirement-tag">第一单元</span>
                        <span class="requirement-tag">单元作文</span>
                        <span class="requirement-tag">全命题</span>
                    </div>

                    <!-- 要求详情 -->
                    <div class="requirement-item">
                        <span class="requirement-label">作文命题</span>
                        <span class="requirement-value">我的植物朋友</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-label">字数要求</span>
                        <span class="requirement-value">300字</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-label">分值</span>
                        <span class="requirement-value">30分</span>
                    </div>

                    <!-- 作文要求描述 -->
                    <div class="requirement-description">
                        <strong>作文要求：</strong>要求一句具体的作文要求描述（可以是科学性要求），也可以是日记形式的要求（如），一定要有具体的要求，要求要具体，明确，清晰，要求要有针对性，要求要有指导性。
                    </div>

                    <!-- 上传作文区域 -->
                    <div class="upload-section">
                        <div class="upload-title">
                            <span>上传作文</span>
                            <div class="help-icon">?</div>
                        </div>
                        <div class="upload-grid">
                            <div class="upload-box">
                                <div class="chart-line line-1"></div>
                                <div class="chart-line line-2"></div>
                            </div>
                            <div class="upload-box">
                                <div class="chart-line line-1"></div>
                                <div class="chart-line line-2"></div>
                            </div>
                            <div class="upload-box">
                                <div class="chart-line line-1"></div>
                                <div class="chart-line line-2"></div>
                            </div>
                            <div class="upload-box">
                                <div class="chart-line line-1"></div>
                                <div class="chart-line line-2"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        // 标签切换功能
        function switchTab(tabName) {
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 为当前点击的标签添加active类
            event.target.classList.add('active');

            // 隐藏所有内容区域
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.style.display = 'none');

            // 显示对应的内容区域
            const targetContent = document.getElementById(tabName + '-content');
            if (targetContent) {
                targetContent.style.display = 'block';
            }
        }

        // 复制全文功能
        function copyEssay() {
            const essayContent = document.querySelector('#model-essay-content .essay-content');
            const text = essayContent.innerText;

            navigator.clipboard.writeText(text).then(function() {
                alert('作文已复制到剪贴板');
            }).catch(function() {
                alert('复制失败，请手动复制');
            });
        }

        // 下载功能
        function downloadEssay() {
            const essayContent = document.querySelector('#model-essay-content .essay-content');
            const title = document.querySelector('#model-essay-content .essay-subtitle');
            const text = title.innerText + '\n\n' + essayContent.innerText;

            const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '我的植物朋友.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示作文点评内容
            document.getElementById('essay-review-content').style.display = 'block';
        });
    </script>
</body>
</html>
